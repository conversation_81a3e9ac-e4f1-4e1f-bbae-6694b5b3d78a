/* Leaflet CSS */
@import 'leaflet/dist/leaflet.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* CSS Custom Properties for theming */
:root {
  --gradient-primary: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  --gradient-secondary: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
  --gradient-accent: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
  --gradient-background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  --shadow-soft: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);
  --shadow-medium: 0 4px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Global styles */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON>e UI', '<PERSON><PERSON>', 'Oxygen',
    'Ubuntu', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>s', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: var(--gradient-background);
  min-height: 100vh;
}

/* Smooth transitions for interactive elements */
button, input, select, textarea {
  transition: all 0.2s ease-in-out;
}

/* Focus styles */
button:focus-visible,
input:focus-visible,
select:focus-visible,
textarea:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Fix for Leaflet marker icons */
.leaflet-marker-icon {
  margin-left: -12px !important;
  margin-top: -41px !important;
}

/* Enhanced map controls */
.map-controls {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  box-shadow: var(--shadow-medium);
  padding: 20px;
  max-width: 320px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Hotspot square styles */
.hotspot-square {
  border-radius: 2px;
  border: 1px solid rgba(255, 255, 255, 0.8);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease-in-out;
  cursor: pointer;
  position: relative;
}

.hotspot-square:hover {
  transform: scale(1.1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  border-color: rgba(255, 255, 255, 1);
  z-index: 1000;
}

/* Size variations for hotspot squares */
.hotspot-size-8 { width: 8px; height: 8px; }
.hotspot-size-9 { width: 9px; height: 9px; }
.hotspot-size-10 { width: 10px; height: 10px; }
.hotspot-size-11 { width: 11px; height: 11px; }
.hotspot-size-12 { width: 12px; height: 12px; }
.hotspot-size-13 { width: 13px; height: 13px; }
.hotspot-size-14 { width: 14px; height: 14px; }
.hotspot-size-15 { width: 15px; height: 15px; }
.hotspot-size-16 { width: 16px; height: 16px; }
.hotspot-size-17 { width: 17px; height: 17px; }
.hotspot-size-18 { width: 18px; height: 18px; }
.hotspot-size-19 { width: 19px; height: 19px; }
.hotspot-size-20 { width: 20px; height: 20px; }
.hotspot-size-21 { width: 21px; height: 21px; }
.hotspot-size-22 { width: 22px; height: 22px; }
.hotspot-size-23 { width: 23px; height: 23px; }
.hotspot-size-24 { width: 24px; height: 24px; }

/* Observation point marker styles */
.observation-point-marker {
  cursor: pointer;
  z-index: 500;
}

.observation-point-marker div {
  transition: all 0.2s ease-in-out;
  position: relative;
}

.observation-point-marker:hover div {
  transform: scale(1.2);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.4) !important;
  z-index: 1000;
}

/* Hotspot popup styles */
.hotspot-popup .leaflet-popup-content-wrapper,
.observation-point-popup .leaflet-popup-content-wrapper {
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.hotspot-popup .leaflet-popup-content,
.observation-point-popup .leaflet-popup-content {
  margin: 0;
  padding: 0;
}

.hotspot-popup .leaflet-popup-tip,
.observation-point-popup .leaflet-popup-tip {
  background: white;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

/* Enhanced popup styles for observation points */
.observation-point-popup .leaflet-popup-content-wrapper {
  min-width: 200px;
}

.observation-point-popup .leaflet-popup-content {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

/* Grid popup styles */
.grid-popup .leaflet-popup-content-wrapper {
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(0, 0, 0, 0.1);
  min-width: 250px;
}

.grid-popup .leaflet-popup-content {
  margin: 0;
  padding: 0;
}

.grid-popup .leaflet-popup-tip {
  background: white;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

/* Utility classes */
@layer components {
  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-all duration-200 shadow-soft hover:shadow-medium;
  }

  .btn-secondary {
    @apply bg-white hover:bg-gray-50 text-gray-700 font-medium py-2 px-4 rounded-lg border border-gray-300 transition-all duration-200 shadow-soft hover:shadow-medium;
  }

  .input-field {
    @apply border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200 bg-white shadow-soft;
  }

  .card {
    @apply bg-white rounded-xl shadow-soft border border-gray-100 p-6 transition-all duration-200 hover:shadow-medium;
  }

  .glass-effect {
    @apply bg-white/80 backdrop-blur-sm border border-white/20;
  }
}
